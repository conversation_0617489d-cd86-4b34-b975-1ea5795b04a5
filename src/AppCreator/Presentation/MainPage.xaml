<Page x:Class="AppCreator.Presentation.MainPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="using:AppCreator.Presentation"
      xmlns:uen="using:Uno.Extensions.Navigation.UI"
      xmlns:utu="using:Uno.Toolkit.UI"
      NavigationCacheMode="Required"
      Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

  <ScrollViewer IsTabStop="True">
    <Grid utu:SafeArea.Insets="VisibleBounds">
      <Grid.RowDefinitions>
        <RowDefinition Height="Auto" />
        <RowDefinition />
      </Grid.RowDefinitions>
      <utu:NavigationBar Content="{Binding Title}" />

        <StackPanel Grid.Row="1" Margin="40" MaxWidth="800" HorizontalAlignment="Center">

            <TextBlock Text="Uno App Generator" 
                        FontSize="32" 
                        FontWeight="Bold" 
                        HorizontalAlignment="Center" 
                        Margin="0,0,0,40"/>

            <TextBlock Text="Describe the app you want to create:" 
                        FontSize="16" 
                        Margin="0,0,0,10"/>

            <TextBox x:Name="AppDescriptionTextBox" 
                        Height="120" 
                        TextWrapping="Wrap" 
                        AcceptsReturn="True"
                        PlaceholderText="e.g., A simple calculator app with basic arithmetic operations"
                        Margin="0,0,0,20"/>

            <Button x:Name="GenerateButton" 
                    Content="Generate App" 
                    Click="OnGenerateAppClick"
                    HorizontalAlignment="Center"
                    MinWidth="150"
                    Height="40"
                    FontSize="16"
                    Margin="0,0,0,20"/>

            <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Center"
                        Margin="0,0,0,20">
                <ProgressRing x:Name="ProgressRing" 
                                IsActive="False" 
                                Width="20" 
                                Height="20"
                                Margin="0,0,10,0"/>
                <TextBlock x:Name="StatusTextBlock" 
                            FontSize="14" 
                            Foreground="Gray"
                            VerticalAlignment="Center"/>
            </StackPanel>

            <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                    CornerRadius="8"
                    Padding="20"
                    Margin="0,20,0,0">
                <StackPanel>
                    <TextBlock Text="Instructions:" 
                                FontWeight="SemiBold" 
                                FontSize="16"
                                Margin="0,0,0,10"/>
                    <TextBlock TextWrapping="Wrap">
                        <Run Text="1. Enter a description of the app you want to create"/>
                        <LineBreak/>
                        <Run Text="2. Click 'Generate App' to create the Uno Platform app"/>
                        <LineBreak/>
                        <Run Text="3. Generated apps will be saved to C:\GeneratedApps\"/>
                        <LineBreak/>
                        <Run Text="4. Make sure you have set your OpenAI API key in the OPENAI_API_KEY environment variable"/>
                    </TextBlock>
                </StackPanel>
            </Border>

        </StackPanel>        
    </Grid>
  </ScrollViewer>
</Page>
