using AppCreator.Services;

namespace AppCreator.Presentation;

public sealed partial class MainPage : Page
{
    public MainPage()
    {
        this.InitializeComponent();
    }

    private async void OnGenerateAppClick(object sender, RoutedEventArgs e)
    {
        var appGeneratorService = ((MainViewModel) DataContext).AppGeneratorService;

        if (appGeneratorService == null)
        {
            StatusTextBlock.Text = "Service not available. Please check configuration.";
            return;
        }

        var description = AppDescriptionTextBox.Text?.Trim();
        if (string.IsNullOrEmpty(description))
        {
            StatusTextBlock.Text = "Please enter an app description.";
            return;
        }

        try
        {
            GenerateButton.IsEnabled = false;
            StatusTextBlock.Text = "Generating app... This may take a moment.";
            ProgressRing.IsActive = true;

            var success = await appGeneratorService.GenerateAppAsync(description);

            if (success)
            {
                StatusTextBlock.Text = "App generated successfully in C:\\GeneratedApps\\";
            }
            else
            {
                StatusTextBlock.Text = "Failed to generate app. Check logs for details.";
            }
        }
        catch (Exception ex)
        {
            StatusTextBlock.Text = $"Error: {ex.Message}";
        }
        finally
        {
            GenerateButton.IsEnabled = true;
            ProgressRing.IsActive = false;
        }
    }
}
