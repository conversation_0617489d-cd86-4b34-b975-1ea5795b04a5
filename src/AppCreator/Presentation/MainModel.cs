using AppCreator.Services;

namespace AppCreator.Presentation;

public partial record MainModel
{
    private INavigator _navigator;
    
    public MainModel(
        IStringLocalizer localizer,
        IOptions<AppConfig> appInfo,
        INavigator navigator,
        AppGeneratorService appGeneratorService)
    {
        _navigator = navigator;
        AppGeneratorService = appGeneratorService;

        Title = "Main";
        Title += $" - {localizer["ApplicationName"]}";
        Title += $" - {appInfo?.Value?.Environment}";
    }

    public string? Title { get; }

    public AppGeneratorService AppGeneratorService { get; }

    public IState<string> Name => State<string>.Value(this, () => string.Empty);

    public async Task GoToSecond()
    {
        var name = await Name;
        await _navigator.NavigateViewModelAsync<SecondModel>(this, data: new Entity(name!));
    }
}
