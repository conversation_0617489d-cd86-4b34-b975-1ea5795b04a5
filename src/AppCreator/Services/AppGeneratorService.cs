using Microsoft.Extensions.AI;
using System.Text.Json;

namespace AppCreator.Services;

public class AppGeneratorService
{
    private readonly IChatClient _chatClient;
    private const string OutputDirectory = @"C:\GeneratedApps";

    public AppGeneratorService(IChatClient chatClient)
    {
        _chatClient = chatClient;
    }

    public async Task<bool> GenerateAppAsync(string userDescription)
    {
        try
        {
            // Create the enhanced prompt with hardcoded Uno app requirement
            var enhancedPrompt = $@"Create a complete Uno Platform desktop application (Skia/WPF) based on this description: {userDescription}

IMPORTANT: This must be an Uno Platform app using Skia for desktop with a single page.

Please provide the complete project structure with the following files:
1. Project file (.csproj)
2. App.xaml and App.xaml.cs
3. MainPage.xaml and MainPage.xaml.cs
4. Program.cs
5. Any additional code files needed

Requirements:
- Use Uno Platform 5.x
- Target .NET 8
- Desktop-only using Skia.Wpf
- Single page application
- Include all necessary NuGet package references
- Provide complete, compilable code
- Follow Uno Platform best practices

Format your response as JSON with this structure:
{{
  ""projectName"": ""AppName"",
  ""files"": [
    {{
      ""path"": ""relative/path/to/file.ext"",
      ""content"": ""file content here""
    }}
  ]
}}";

            // Call the LLM to generate the app
            var response = await _chatClient.GetResponseAsync(enhancedPrompt);
            var generatedContent = response.Messages.First().Text;

            if (string.IsNullOrEmpty(generatedContent))
            {
                return false;
            }

            // Parse the JSON response
            var appData = ParseGeneratedContent(generatedContent);
            if (appData == null)
            {
                return false;
            }

            // Create the output directory
            var projectDirectory = Path.Combine(OutputDirectory, appData.ProjectName);
            Directory.CreateDirectory(projectDirectory);

            // Write all the generated files
            foreach (var file in appData.Files)
            {
                var filePath = Path.Combine(projectDirectory, file.Path);
                var fileDirectory = Path.GetDirectoryName(filePath);
                
                if (!string.IsNullOrEmpty(fileDirectory))
                {
                    Directory.CreateDirectory(fileDirectory);
                }

                await File.WriteAllTextAsync(filePath, file.Content);
            }

            return true;
        }
        catch (Exception ex)
        {
            // Log the exception (you might want to use proper logging)
            System.Diagnostics.Debug.WriteLine($"Error generating app: {ex.Message}");
            return false;
        }
    }

    private GeneratedAppData? ParseGeneratedContent(string content)
    {
        try
        {
            // Try to extract JSON from the response (LLM might include extra text)
            var jsonStart = content.IndexOf('{');
            var jsonEnd = content.LastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonContent = content.Substring(jsonStart, jsonEnd - jsonStart + 1);
                return JsonSerializer.Deserialize<GeneratedAppData>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }

            // Fallback: try to parse the entire content as JSON
            return JsonSerializer.Deserialize<GeneratedAppData>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        catch
        {
            // If JSON parsing fails, create a simple structure with the raw content
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            return new GeneratedAppData
            {
                ProjectName = $"GeneratedApp_{timestamp}",
                Files = new[]
                {
                    new GeneratedFile
                    {
                        Path = "README.md",
                        Content = $"# Generated App\n\nGenerated content:\n\n```\n{content}\n```"
                    }
                }
            };
        }
    }

    private class GeneratedAppData
    {
        public string ProjectName { get; set; } = "";
        public GeneratedFile[] Files { get; set; } = Array.Empty<GeneratedFile>();
    }

    private class GeneratedFile
    {
        public string Path { get; set; } = "";
        public string Content { get; set; } = "";
    }
}
