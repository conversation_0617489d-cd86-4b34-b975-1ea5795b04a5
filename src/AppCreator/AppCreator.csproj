<Project Sdk="Uno.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net9.0-desktop</TargetFrameworks>

    <OutputType>Exe</OutputType>
    <UnoSingleProject>true</UnoSingleProject>

    <!-- Display name -->
    <ApplicationTitle>AppCreator</ApplicationTitle>
    <!-- App Identifier -->
    <ApplicationId>com.companyname.AppCreator</ApplicationId>
    <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <!-- Package Publisher -->
    <ApplicationPublisher>bret</ApplicationPublisher>
    <!-- Package Description -->
    <Description>AppCreator powered by Uno Platform.</Description>

    <!--
      UnoFeatures let's you quickly add and manage implicit package references based on the features you want to use.
      https://aka.platform.uno/singleproject-features
    -->
    <UnoFeatures>
      Lottie;
      Hosting;
      Toolkit;
      Logging;
      MVUX;
      Configuration;
      HttpKiota;
      Serialization;
      Localization;
      Navigation;
      ThemeService;
      SkiaRenderer;
    </UnoFeatures>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.AI" />
    <PackageReference Include="Microsoft.Extensions.AI.OpenAI" />
  </ItemGroup>

</Project>
