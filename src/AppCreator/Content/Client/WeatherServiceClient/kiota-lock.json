{"descriptionHash": "AD931E25C1E8816EAC2406765686136B1AB80E4367C4ED5521F8A20DC8FBBE346770AA4FA36E041C9733DB4D3E074414875D6A0679911135BE1FE79740217E42", "descriptionLocation": "../../../Specs/WeatherForecast.swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.16.0", "clientClassName": "WeatherServiceClient", "clientNamespaceName": "AppCreator.Client", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}