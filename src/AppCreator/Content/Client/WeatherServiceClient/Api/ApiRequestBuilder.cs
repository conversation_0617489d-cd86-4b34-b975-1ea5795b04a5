// <auto-generated/>
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using AppCreator.Client.Api.Weatherforecast;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Extensions;

namespace AppCreator.Client.Api;
/// <summary>
/// Builds and executes requests for operations under \api
/// </summary>
[global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.16.0")]
public partial class ApiRequestBuilder : BaseRequestBuilder
{
    /// <summary>The weatherforecast property</summary>
    public global::AppCreator.Client.Api.Weatherforecast.WeatherforecastRequestBuilder Weatherforecast
    {
        get => new global::AppCreator.Client.Api.Weatherforecast.WeatherforecastRequestBuilder(PathParameters, RequestAdapter);
    }
    /// <summary>
    /// Instantiates a new <see cref="global::AppCreator.Client.Api.ApiRequestBuilder"/> and sets the default values.
    /// </summary>
    /// <param name="pathParameters">Path parameters for the request</param>
    /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
    public ApiRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", pathParameters)
    {
    }
    /// <summary>
    /// Instantiates a new <see cref="global::AppCreator.Client.Api.ApiRequestBuilder"/> and sets the default values.
    /// </summary>
    /// <param name="rawUrl">The raw URL to use for the request builder.</param>
    /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
    public ApiRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", rawUrl)
    {
    }
}
