global using System.Collections.Immutable;
global using AppCreator.Client;
global using AppCreator.DataContracts;
global using AppCreator.DataContracts.Serialization;
global using AppCreator.Models;
global using AppCreator.Presentation;
global using AppCreator.Services.Caching;
global using AppCreator.Services.Endpoints;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Localization;
global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Options;
global using Uno.Extensions.Http.Kiota;
global using ApplicationExecutionState = Windows.ApplicationModel.Activation.ApplicationExecutionState;
[assembly: Uno.Extensions.Reactive.Config.BindableGenerationTool(3)]
